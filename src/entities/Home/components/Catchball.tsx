import {
  DndContext,
  DragEndEvent,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { Container } from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { DeliverableItemsResponse } from '~/shared/types/DeliverablesService';
import { ProposalItemsResponse } from '~/shared/types/ProposalService';

import { CatalogList } from './Catalog/CatalogList';
import { ProposalList } from './Proposal/ProposalList';

interface CatchballProps {
  initialKpis: DeliverableItemsResponse;
  initialProposals: ProposalItemsResponse;
}

export function Catchball({ initialKpis, initialProposals }: CatchballProps) {
  const { t } = useTranslate();
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 3,
      },
    }),
  );

  const handleDragStart = (event: DragStartEvent) => {
    console.log('Drag start', event);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    console.log('Drag end', event);
  };

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <Container className="flex h-[calc(100vh-240px)] gap-6">
        <CatalogList title={t('common_catalog')} initialKpis={initialKpis} />
        <ProposalList initialProposals={initialProposals} />
      </Container>
    </DndContext>
  );
}
