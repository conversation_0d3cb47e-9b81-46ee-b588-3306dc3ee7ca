import {
  ArrowsAngleExpand,
  ArrowUpRightSquare,
  Clock,
  ThreeDots,
} from 'react-bootstrap-icons';
import { useRouter } from 'next/router';
import {
  Button,
  DropdownMenu,
  IconWrapper,
  Separator,
  Typography,
} from '@ghq-abi/design-system-v2';
import clsx from 'clsx';

import { Avatar } from '~/shared/components';
import { AcceptedIcon, InProgress, SkipIcon } from '~/shared/components/icons';
import { Proposal } from '~/shared/types/Proposal';
import { ProposalStatusEnum as ProposalStatus } from '~/shared/utils/enums';

export interface ProposalCardProps {
  data: Proposal;
  showDrawerAction?: boolean;
  showOptionAction?: boolean;
  showDetailAction?: boolean;
  showActions?: boolean;
  options?: {
    value: string;
    label: string;
    icon: React.ReactNode;
    onClick?: () => void;
  }[];
  onDrawerActionClick?: () => void;
  onDetailActionClick?: () => void;
  onOptionActionClick?: () => void;
}

export const ProposalCard = ({
  data,
  showActions = false,
  showOptionAction = false,
  showDetailAction = false,
  showDrawerAction = false,
  onDrawerActionClick,
  onDetailActionClick,
  onOptionActionClick,
  options,
}: ProposalCardProps) => {
  const router = useRouter();

  const mappedStatus = (status: ProposalStatus) => {
    if (
      [
        ProposalStatus.IN_PROGRESS_FINAL,
        ProposalStatus.IN_PROGRESS_FEEDBACK,
        ProposalStatus.IN_PROGRESS_PROPOSAL,
      ].includes(status)
    ) {
      return {
        label: 'In Progress',
        icon: <InProgress color="#E3C90E" />,
      };
    }

    const MAP_STATUS = {
      [ProposalStatus.NOT_STARTED]: {
        label: 'Not Started',
        icon: <SkipIcon color="#7D8597" />,
      },
      [ProposalStatus.COMPLETED]: {
        label: 'Completed',
        icon: <AcceptedIcon color="#44AC21" />,
      },
    };

    return MAP_STATUS[status as keyof typeof MAP_STATUS];
  };

  const handleDetailClick = async () => {
    await router.push(`/proposals/${data.uid}`);
  };

  const PROPOSAL_COUNT =
    data.targets?.filter(target =>
      target.targetTypes?.some(type => type.type === 'PROPOSAL'),
    ).length ?? 0;

  const FEEDBACK_COUNT =
    data.targets?.filter(target =>
      target.targetTypes?.some(type => type.type === 'FEEDBACK'),
    ).length ?? 0;

  return (
    <div
      className={`
      relative bg-white border border-gray-200 rounded-lg  min-h-[168px] transition-all duration-200
    `}
    >
      <div className="flex flex-col  gap-2">
        <div className="flex items-start flex-col gap-3 p-4">
          {showActions && (
            <div className="flex w-full items-center gap-2 justify-between">
              <div
                className={clsx(
                  'rounded-md p-1 flex items-center gap-1 justify-center',
                  {
                    'bg-[#FBF2B3]': [
                      ProposalStatus.IN_PROGRESS_FINAL,
                      ProposalStatus.IN_PROGRESS_FEEDBACK,
                      ProposalStatus.IN_PROGRESS_PROPOSAL,
                    ].includes(data.status as ProposalStatus),
                    'bg-[#191F2E14]':
                      data.status === ProposalStatus.NOT_STARTED,
                    'bg-[#EBFCD5]': data.status === ProposalStatus.COMPLETED,
                  },
                )}
              >
                {mappedStatus(data.status as ProposalStatus).icon}
                <Typography
                  variant="metadata-sm-regular"
                  className={clsx({
                    'text-[#7D6F0B]': [
                      ProposalStatus.IN_PROGRESS_FINAL,
                      ProposalStatus.IN_PROGRESS_FEEDBACK,
                      ProposalStatus.IN_PROGRESS_PROPOSAL,
                    ].includes(data.status as ProposalStatus),
                    'text-[#7D8597]':
                      data.status === ProposalStatus.NOT_STARTED,
                    'text-[#44AC21]': data.status === ProposalStatus.COMPLETED,
                  })}
                >
                  {mappedStatus(data.status as ProposalStatus).label}
                </Typography>
              </div>
              <div className="flex items-center gap-2">
                {showDrawerAction && (
                  <Button
                    variant="tertiary"
                    size="icon"
                    onClick={e => {
                      e.stopPropagation();
                      e.preventDefault();
                      onDrawerActionClick?.();
                    }}
                    onPointerDown={e => e.stopPropagation()}
                  >
                    <ArrowUpRightSquare />
                  </Button>
                )}
                {showDetailAction && (
                  <Button
                    variant="tertiary"
                    size="icon"
                    onClick={handleDetailClick}
                    onPointerDown={e => e.stopPropagation()}
                  >
                    <ArrowsAngleExpand />
                  </Button>
                )}
                {showOptionAction && (
                  <DropdownMenu.Root>
                    <DropdownMenu.Trigger asChild>
                      <Button
                        variant="tertiary"
                        size="icon"
                        onClick={e => {
                          e.stopPropagation();
                          e.preventDefault();
                          onOptionActionClick?.();
                        }}
                        onPointerDown={e => e.stopPropagation()}
                      >
                        <ThreeDots />
                      </Button>
                    </DropdownMenu.Trigger>
                    <DropdownMenu.Content className="w-48">
                      <DropdownMenu.RadioGroup
                        className="flex flex-col gap-2"
                        value={''}
                        onValueChange={() => {}}
                      >
                        {options?.map(option => (
                          <DropdownMenu.RadioItem
                            key={option.value}
                            value={option.value}
                            className="w-full flex items-center gap-2"
                            onClick={option.onClick}
                          >
                            <IconWrapper
                              variant="default"
                              round="none"
                              size={26}
                            >
                              {option.icon}
                            </IconWrapper>
                            <Typography variant="body-sm-regular">
                              {option.label}
                            </Typography>
                          </DropdownMenu.RadioItem>
                        ))}
                      </DropdownMenu.RadioGroup>
                    </DropdownMenu.Content>
                  </DropdownMenu.Root>
                )}
              </div>
            </div>
          )}
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-4">
              <Avatar
                name={data.employee?.name}
                globalId={`${data.employee?.globalId}`}
                size={50}
              />
              <div className="flex flex-col gap-1">
                <Typography variant="body-md-bold">
                  {data.employee?.name}
                </Typography>
                <Typography variant="metadata-sm-regular">
                  {data.employee?.businessFunction}
                </Typography>
              </div>
            </div>
          </div>
        </div>
        <Separator className="text-[#7D8597]" />
        <div className="flex items-center gap-2 p-4 justify-between">
          <Typography variant="body-sm-regular">
            Proposal ({PROPOSAL_COUNT})
          </Typography>
          <Typography variant="body-sm-regular">
            Feedback ({FEEDBACK_COUNT})
          </Typography>
        </div>
      </div>
    </div>
  );
};
