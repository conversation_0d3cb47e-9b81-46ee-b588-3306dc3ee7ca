import { DeliverableItem } from '~/shared/types/Deliverable';
import { Target } from '~/shared/types/Target';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

export type CreateTargetDrawerProps = {
  proposalId: string;
  isOpen: boolean;
  isEdit: boolean;
  onClose: () => void;
  onSuccessSubmit: (response: any) => void;
  data?: DeliverableItem | Target | undefined;
  targetType?: TargetTypeEnum;
};

export type FormValues = {
  definition: string;
  calculationMethod: string;
  weight: number;
  scope: string;
};

export type CreateEditTargetBody = {
  targets: Target[];
};
