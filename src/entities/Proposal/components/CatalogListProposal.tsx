import { Container, Skeleton, Typography } from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';
import { CatalogCard } from '~/entities/Home/components';
import { CatalogFilter } from '~/entities/Home/components/Catalog/CatalogFilter';

import { useCatalogFilter } from '~/entities/Home/hooks/useCatalogFilter';
import { NoResults } from '~/shared/components';
import { DeliverableItem } from '~/shared/types/Deliverable';

export interface CatalogListProps {
  title?: string;
  subtitle?: string;
  className?: string;
  containerClassName?: string;
  isSearchLoading?: boolean;
  isInitialLoading?: boolean;
  isError?: boolean;
  deliverables: DeliverableItem[];
  catalogFilterHook: ReturnType<typeof useCatalogFilter>;
}

export function CatalogListProposal({
  title,
  subtitle,
  className = '',
  containerClassName = 'w-96 bg-white rounded-lg p-6 flex flex-col',
  isSearchLoading = false,
  isInitialLoading = false,
  isError = false,
  deliverables = [],
  catalogFilterHook
}: CatalogListProps) {
  const { t } = useTranslate();

  return (
    <div className={`${containerClassName} ${className}`}>
      <div className="flex flex-col mb-4 flex-shrink-0">
        {(title || subtitle) && (
          <>
            {isInitialLoading && title && (
              <Skeleton className="w-full h-6 mb-2" />
            )}
            {!isInitialLoading && title && (
              <Typography variant="body-sm-bold" className="text-gray-900 mb-2">
                {title}
              </Typography>
            )}

            {isInitialLoading && subtitle && (
              <Skeleton className="w-full h-4 mb-2" />
            )}
            {!isInitialLoading && subtitle && (
              <Typography
                variant="body-sm-regular"
                className="text-gray-500 mb-4"
              >
                {subtitle}
              </Typography>
            )}
          </>
        )}

        {isInitialLoading &&
          Array.from({ length: 2 }).map((_, index) => (
            <Skeleton key={index} className="w-full h-10 mb-4" />
          ))}
        {!isInitialLoading && <CatalogFilter {...catalogFilterHook} />}
      </div>

      <div className="space-y-3 flex-1 min-h-0 overflow-y-auto">
        {isError && (
          <Container className="flex justify-center items-center py-8">
            <Typography variant="metadata-sm-medium">
              {t('common_something_went_wrong')}
            </Typography>
          </Container>
        )}
        {isInitialLoading || isSearchLoading ? (
          Array.from({ length: 4 }).map((_, index) => (
            <Skeleton key={index} className="w-full h-[174px]" />
          ))
        ) : deliverables?.length && deliverables.length > 0 ? (
          deliverables.map(deliverable => (
            <div
              className="cursor-pointer mb-3 overflow-x-hidden"
              key={deliverable.uid}
            >
              <CatalogCard
                data={deliverable}
                isSelected={deliverables.some(
                  d => d.uid === deliverable.uid,
                )}
                showActions={true}
                showDefinition={true}
                showDrawerAction={true}
              />
            </div>
          ))
        ) : (
          // TODO: Add empty state DS and apply here
          <NoResults />
        )}
      </div>
    </div>
  );
}
