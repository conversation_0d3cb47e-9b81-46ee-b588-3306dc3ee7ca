import React from 'react';
import { Trash } from 'react-bootstrap-icons';
import {
  DndContext,
  DragOverlay,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { But<PERSON>, FooterActionBar } from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { CatalogCard } from '~/entities/Home/components';
import { useCatalogFilter } from '~/entities/Home/hooks/useCatalogFilter';
import { ChildCard, TargetCard } from '~/shared/components';
import { ActionModal } from '~/shared/components/ActionModal';
import { DeliverableItem } from '~/shared/types/Deliverable';
import { Target } from '~/shared/types/Target';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { CatalogWithTabs } from '../Feedback/CatalogWithTabs';
import { CreateTargetDrawer } from '../Proposal/CreateTargetDrawer';
import { DroppableTarget } from '../Proposal/DroppableTarget';
import { TargetsList } from '../Proposal/TargetsList';

import { FinalDragProps } from './types';
import { useFinal } from './useFinal';

export function FinalDrag({
  proposalStatus,
  proposalUid,
  targets,
  allTargets = [],
  onProposalUpdate,
}: FinalDragProps) {
  const { t } = useTranslate();
  const catalogFilterHook = useCatalogFilter();

  const feedbackTargets = allTargets.filter(target =>
    target.targetTypes?.some(
      targetType => targetType.type === TargetTypeEnum.FEEDBACK,
    ),
  );

  const {
    selectedTargets,
    acceptedTargetUids,
    draggedItem,
    isDraggingNewItem,
    isOpenDrawer,
    drawerDeliverable,
    isLoading,
    isLoadingDelete,
    isLoadingMergeTargets,
    handleAcceptTarget,
    handleDragStart,
    handleDragEnd,
    handleSubmit,
    handleClearDeliverable,
    handleRemoveTargets,
    onDrawerSuccessSubmit,
    setIsOpenDrawer,
    availableDeliverables,
    selectedFinalTargetUids,
    isTargetLoading,
    actionModal,
  } = useFinal({
    proposalUid,
    targets,
    allTargets,
    catalogFilterHook,
    onProposalUpdate,
  });

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 3,
      },
    }),
  );

  return (
    <>
      <DndContext
        sensors={sensors}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <div className="flex h-[calc(100vh-340px)] gap-6">
          <CatalogWithTabs
            deliverables={availableDeliverables}
            proposalTargets={allTargets}
            isInitialLoading={false}
            isSearchLoading={false}
            isError={false}
            catalogFilterHook={catalogFilterHook}
            onAcceptTarget={handleAcceptTarget}
            acceptedTargetUids={[
              ...acceptedTargetUids,
              ...Array.from(selectedFinalTargetUids),
            ]}
            isTargetLoading={isTargetLoading}
            showProposalTargetType={TargetTypeEnum.PROPOSAL}
            showFeedbackTab={true}
            showBadgesInProposalTab={true}
          />
          <TargetsList
            selectedData={selectedTargets}
            isDraggingNewItem={isDraggingNewItem}
            actions={[
              {
                label: t('common_clear_list'),
                onClick: handleClearDeliverable,
                variant: 'secondary',
                iconLeft: <Trash />,
                disabled: selectedTargets.length === 0,
              },
            ]}
          >
            {selectedTargets.map(target => {
              if (target.children && target.children.length > 1) {
                return (
                  <DroppableTarget
                    isDragging={isDraggingNewItem}
                    key={target.uid}
                    target={target}
                  >
                    <TargetCard
                      key={target.uid}
                      data={target}
                      proposalStatus={proposalStatus}
                      hideChildren={target.children.length <= 1}
                      currentTargetType={TargetTypeEnum.FINAL}
                      onRemoveActionClick={handleRemoveTargets}
                      isOnDroppableArea
                    />
                  </DroppableTarget>
                );
              }
              return (
                <DroppableTarget
                  isDragging={isDraggingNewItem}
                  key={target.uid}
                  target={target}
                >
                  <ChildCard
                    key={target.uid}
                    target={target}
                    disableDrag={false}
                    onRemoveActionClick={handleRemoveTargets}
                    showActions
                  />
                </DroppableTarget>
              );
            })}
          </TargetsList>
        </div>
        <DragOverlay>
          {(draggedItem as Target)?.deliverable ? (
            <ChildCard target={draggedItem as Target} />
          ) : (
            <CatalogCard data={draggedItem as DeliverableItem} isDragging />
          )}
        </DragOverlay>
      </DndContext>
      <CreateTargetDrawer
        isOpen={isOpenDrawer}
        onClose={() => setIsOpenDrawer(false)}
        data={drawerDeliverable}
        isEdit={false}
        proposalId={proposalUid}
        onSuccessSubmit={onDrawerSuccessSubmit}
        targetType={TargetTypeEnum.FINAL}
      />
      <ActionModal
        isOpen={actionModal.isOpen}
        openModal={actionModal.openModal}
        closeModal={actionModal.closeModal}
        title={actionModal.title}
        message={actionModal.message}
        actions={[
          {
            label: t('common_yes'),
            onClick: actionModal.handleConfirm,
            variant: 'primary',
            isLoading: isLoadingMergeTargets,
          },
          {
            label: t('common_no'),
            onClick: actionModal.closeModal,
            variant: 'secondary',
          },
        ]}
      />
      <FooterActionBar>
        <Button
          variant="secondary"
          border="default"
          className="w-fit"
          onClick={() => window.history.back()}
        >
          Back
        </Button>
        <Button
          id="deliverable-form"
          isLoading={isLoading}
          variant="primary"
          className="w-fit"
          round="md"
          onClick={handleSubmit}
        >
          {t('common_submit')}
        </Button>
      </FooterActionBar>
    </>
  );
}
