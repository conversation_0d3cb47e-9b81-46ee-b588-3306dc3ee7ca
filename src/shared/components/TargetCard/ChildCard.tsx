import {
  ArrowUpR<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ChatSquareT<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Person,
  Trash,
} from 'react-bootstrap-icons';
import { useDraggable } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import {
  <PERSON><PERSON>,
  Card,
  Container,
  IconWrapper,
  Typography,
} from '@ghq-abi/design-system-v2';

import { DeliverableTypeEnum } from '~/shared/types/Deliverable';
import { Target } from '~/shared/types/Target';
import { cn } from '~/shared/utils/cn';
import { ProposalStatusEnum } from '~/shared/utils/enums';

import { Scale } from '../icons';

interface ChildCardProps {
  target: Target;
  disableDrag?: boolean;
  onEditTarget?: (target: Target) => void;
  onOpenComments?: (target: Target) => void;
  onRemoveActionClick?: (target: Target[]) => void;
  hasManagerPermission?: boolean;
  hasEmployeePermission?: boolean;
  isDrawer?: boolean;
  showActions?: boolean;
}

export function ChildCard({
  target,
  disableDrag,
  onRemoveActionClick,
  onEditTarget,
  onOpenComments,
  hasManagerPermission,
  isDrawer,
  showActions = false,
}: ChildCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging: dragging,
  } = useDraggable({
    id: target?.uid ? target.uid : 'draggable-child-card',
    data: {
      type: 'target',
      data: target,
    },
  });

  const child = target.children?.[0] ? target.children[0] : target;

  const style = {
    transform: CSS.Translate.toString(transform),
    opacity: dragging ? 0 : 1,
  };

  const STATUS_IN_PROGRESS_MAP = [
    ProposalStatusEnum.IN_PROGRESS_PROPOSAL,
    ProposalStatusEnum.IN_PROGRESS_FEEDBACK,
    ProposalStatusEnum.IN_PROGRESS_FINAL,
  ];

  return (
    <div
      ref={!disableDrag ? setNodeRef : undefined}
      style={!disableDrag ? style : undefined}
      {...(!disableDrag ? listeners : {})}
      {...(!disableDrag ? attributes : {})}
      className={`
        ${!disableDrag ? 'cursor-grab' : ''}
        ${
          dragging
            ? '!w-80  shadow-2xl z-[1000] cursor-grabbing transition-all duration-500 scale-75'
            : 'w-full'
        }
      `}
    >
      <Card.Root round="md" className="py-2">
        {showActions && (
          <div className="flex w-full items-center p-[12px] justify-end z-10">
            <Button
              variant="tertiary"
              size="icon"
              onClick={() => onEditTarget?.(child)}
            >
              <ArrowUpRightSquare />
            </Button>
            <Button
              variant="tertiary"
              size="icon"
              onClick={() => onOpenComments?.(target)}
            >
              <ChatSquareText />
            </Button>
            <Button
              variant="tertiary"
              size="icon"
              onClick={() => onRemoveActionClick?.([child])}
            >
              <Trash />
            </Button>
          </div>
        )}
        <Card.Header className="flex flex-row gap-4 items-center pb-1 pt-0">
          {child.deliverable?.type === DeliverableTypeEnum.KPI ||
          child.deliverable?.deliverableType?.code ===
            DeliverableTypeEnum.KPI ? (
            <IconWrapper variant="secondary" round="md" size={42}>
              <BarChartFill size={24} />
            </IconWrapper>
          ) : (
            <IconWrapper variant="primary" round="md" size={42}>
              <ListCheck size={24} />
            </IconWrapper>
          )}
          <Card.Title
            className={cn('flex flex-col max-w-4xl', {
              'max-w-xs': isDrawer,
            })}
          >
            <Typography
              variant="body-md-regular"
              color="dark"
              className="font-semibold whitespace-nowrap overflow-hidden text-ellipsis"
            >
              {child.deliverable?.name}
            </Typography>
            <Container className="flex flex-row items-center gap-4">
              <Typography
                variant="body-sm-regular"
                color="dark"
                className="flex flex-row items-center gap-1"
              >
                <Person />
                {child.deliverable?.usage || 0}
              </Typography>
              <Typography
                variant="body-sm-regular"
                color="dark"
                className="flex flex-row items-center gap-1"
              >
                <Scale />
                {child.weight}%
              </Typography>
            </Container>
          </Card.Title>
        </Card.Header>
        <Card.Content className="flex flex-col gap-2">
          <Typography variant="metadata-sm-regular">
            {child.deliverable?.calculationMethod}
          </Typography>
          <Container className="flex flex-col">
            <Typography variant="metadata-xs-bold" color="dark">
              SCOPE:
            </Typography>
            <Typography variant="metadata-sm-regular">{child.scope}</Typography>
          </Container>
        </Card.Content>
      </Card.Root>
    </div>
  );
}
